<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Special Birthday Message</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen active">
        <div class="loading-content">
            <h1 class="loading-title">🎂 Preparing Something Special 🎂</h1>
            <div class="cricket-container">
                <div class="cricket cricket-1">🦗</div>
                <div class="cricket cricket-2">🦗</div>
                <div class="cricket cricket-3">🦗</div>
                <div class="cricket cricket-4">🦗</div>
                <div class="cricket cricket-5">🦗</div>
            </div>
            <div class="loading-text">Loading your surprise...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="toast">
        <div class="balloon-container">
            <div class="balloon balloon-1">🎈</div>
            <div class="balloon balloon-2">🎈</div>
            <div class="balloon balloon-3">🎈</div>
            <div class="balloon balloon-4">🎈</div>
            <div class="balloon balloon-5">🎈</div>
        </div>
        <div class="toast-message">🎉 Correct! Unlocking your special message... 🎉</div>
    </div>

    <div class="container">
        <!-- Riddle Section -->
        <div id="riddle-section" class="section">
            <div class="hearts-background">
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
            </div>
            
            <div class="content">
                <h1 class="title">🎂 A Special Birthday Surprise 🎂</h1>
                
                <div class="riddle-box">
                    <h2>Solve this riddle to unlock your message:</h2>
                    <div class="riddle-text">
                        <p>I can be sweet, I can be true,</p>
                        <p>I'm often given to someone new.</p>
                        <p>I'm held in hearts, I'm sealed with a kiss,</p>
                        <p>Without me, life might feel amiss.</p>
                        <p class="question">What am I?</p>
                    </div>
                </div>
                
                <div class="answer-section">
                    <input type="text" id="answer-input" placeholder="Enter your answer..." maxlength="20">
                    <button id="submit-btn" onclick="checkAnswer()">Unlock Message</button>
                    <div id="error-message" class="error-message"></div>
                </div>
            </div>
        </div>

        <!-- Birthday Message Section -->
        <div id="message-section" class="section">
            <div class="hearts-background">
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
                <div class="heart"></div>
            </div>
            
            <div class="content">
                <h1 class="birthday-title">Happy Birthday! 🎉💖</h1>
                
                <div class="message-box">
                    <p>Happy Birthday to the most amazing woman in my life! 🎉💖</p>
                    
                    <p>Even though miles separate us today, my heart is right there with you, celebrating the incredible person you are. Distance may keep us apart, but nothing can diminish the love I feel for you—it only grows stronger with each passing day.</p>
                    
                    <p>On your special day, I hope you're surrounded by joy, laughter, and all the love you deserve. You shine brighter than the stars, and I'm so grateful to have you in my life. I wish I could be there to hold your hand, see your smile, and whisper 'I love you' in person… but until then, know that you're in my thoughts every second.</p>
                    
                    <p>Here's to you—beautiful, kind, and unforgettable. May this year bring you endless happiness and dreams come true. I can't wait for the day when we're together again, and I can make up for all these missed moments.</p>
                    
                    <p class="signature">With all my love, always and forever. ❤️🎂</p>
                </div>
                
                <button id="back-btn" onclick="goBack()">Back to Riddle</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
