* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-in-out;
    pointer-events: none;
}

.section.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 100%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 10;
}

.title, .birthday-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    color: #d63384;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.riddle-box {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 30px;
    margin: 30px 0;
    border: 2px solid #ffc0cb;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.riddle-box h2 {
    color: #6f42c1;
    font-size: 1.3rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.riddle-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 8px;
    font-style: italic;
}

.riddle-text .question {
    font-weight: 600;
    color: #d63384;
    font-size: 1.2rem;
    margin-top: 15px;
    font-style: normal;
}

.answer-section {
    margin-top: 30px;
}

#answer-input {
    width: 100%;
    max-width: 300px;
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 2px solid #ffc0cb;
    border-radius: 50px;
    outline: none;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

#answer-input:focus {
    border-color: #d63384;
    box-shadow: 0 0 20px rgba(214, 51, 132, 0.2);
    transform: scale(1.02);
}

#submit-btn, #back-btn {
    background: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(214, 51, 132, 0.3);
    margin: 10px;
}

#submit-btn:hover, #back-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(214, 51, 132, 0.4);
}

.error-message {
    color: #dc3545;
    font-weight: 500;
    margin-top: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-message.show {
    opacity: 1;
}

.message-box {
    text-align: left;
    line-height: 1.8;
    font-size: 1.1rem;
    color: #495057;
}

.message-box p {
    margin-bottom: 20px;
}

.signature {
    text-align: center;
    font-family: 'Dancing Script', cursive;
    font-size: 1.4rem;
    font-weight: 600;
    color: #d63384;
    margin-top: 30px !important;
}

/* Hearts Animation */
.hearts-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.heart {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff69b4;
    transform: rotate(45deg);
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart:before,
.heart:after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff69b4;
    border-radius: 50%;
}

.heart:before {
    top: -10px;
    left: 0;
}

.heart:after {
    top: 0;
    left: -10px;
}

.heart:nth-child(1) { left: 10%; animation-delay: 0s; }
.heart:nth-child(2) { left: 20%; animation-delay: 1s; }
.heart:nth-child(3) { left: 30%; animation-delay: 2s; }
.heart:nth-child(4) { left: 70%; animation-delay: 3s; }
.heart:nth-child(5) { left: 80%; animation-delay: 4s; }
.heart:nth-child(6) { left: 90%; animation-delay: 5s; }
.heart:nth-child(7) { left: 50%; animation-delay: 2.5s; }
.heart:nth-child(8) { left: 60%; animation-delay: 1.5s; }

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(45deg) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
        transform: translateY(90vh) rotate(45deg) scale(1);
    }
    90% {
        opacity: 0.7;
        transform: translateY(-10vh) rotate(45deg) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .title, .birthday-title {
        font-size: 2rem;
    }
    
    .riddle-text p {
        font-size: 1rem;
    }
    
    .message-box {
        font-size: 1rem;
    }
    
    #answer-input {
        font-size: 1rem;
        padding: 12px 15px;
    }
    
    #submit-btn, #back-btn {
        font-size: 1rem;
        padding: 12px 25px;
    }
}

@media (max-width: 480px) {
    .content {
        padding: 25px 15px;
    }
    
    .title, .birthday-title {
        font-size: 1.8rem;
    }
    
    .riddle-box {
        padding: 20px;
    }
}
