* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Loading Screen Styles */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease-in-out;
}

.loading-screen.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 40px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5); }
    to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.8); }
}

.cricket-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 40px 0;
    height: 60px;
}

.cricket {
    font-size: 2rem;
    animation: cricketJump 1.5s ease-in-out infinite;
}

.cricket-1 { animation-delay: 0s; }
.cricket-2 { animation-delay: 0.3s; }
.cricket-3 { animation-delay: 0.6s; }
.cricket-4 { animation-delay: 0.9s; }
.cricket-5 { animation-delay: 1.2s; }

@keyframes cricketJump {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(-10deg); }
    50% { transform: translateY(-30px) rotate(0deg); }
    75% { transform: translateY(-20px) rotate(10deg); }
}

.loading-text {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: pulse 2s ease-in-out infinite;
}

.loading-bar {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin: 0 auto;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff9a9e, #fecfef, #ff9a9e);
    background-size: 200% 100%;
    border-radius: 3px;
    animation: loadingProgress 3s ease-in-out forwards, shimmer 1.5s ease-in-out infinite;
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Toast Notification Styles */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    z-index: 10000;
    text-align: center;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.toast.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.balloon-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.balloon {
    font-size: 2rem;
    animation: balloonPop 0.8s ease-out forwards;
    transform: scale(0);
}

.balloon-1 { animation-delay: 0s; }
.balloon-2 { animation-delay: 0.1s; }
.balloon-3 { animation-delay: 0.2s; }
.balloon-4 { animation-delay: 0.3s; }
.balloon-5 { animation-delay: 0.4s; }

@keyframes balloonPop {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.3) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

.toast-message {
    font-size: 1.2rem;
    font-weight: 600;
    color: #d63384;
    font-family: 'Dancing Script', cursive;
}

.container {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-in-out;
    pointer-events: none;
}

.section.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 100%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 10;
}

.title, .birthday-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    color: #d63384;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.riddle-box {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 30px;
    margin: 30px 0;
    border: 2px solid #ffc0cb;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.riddle-box h2 {
    color: #6f42c1;
    font-size: 1.3rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.riddle-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 8px;
    font-style: italic;
}

.riddle-text .question {
    font-weight: 600;
    color: #d63384;
    font-size: 1.2rem;
    margin-top: 15px;
    font-style: normal;
}

.answer-section {
    margin-top: 30px;
}

#answer-input {
    width: 100%;
    max-width: 300px;
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 2px solid #ffc0cb;
    border-radius: 50px;
    outline: none;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

#answer-input:focus {
    border-color: #d63384;
    box-shadow: 0 0 20px rgba(214, 51, 132, 0.2);
    transform: scale(1.02);
}

#submit-btn, #back-btn {
    background: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(214, 51, 132, 0.3);
    margin: 10px;
}

#submit-btn:hover, #back-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(214, 51, 132, 0.4);
}

.error-message {
    color: #dc3545;
    font-weight: 500;
    margin-top: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-message.show {
    opacity: 1;
}

.message-box {
    text-align: left;
    line-height: 1.8;
    font-size: 1.1rem;
    color: #495057;
}

.message-box p {
    margin-bottom: 20px;
}

.signature {
    text-align: center;
    font-family: 'Dancing Script', cursive;
    font-size: 1.4rem;
    font-weight: 600;
    color: #d63384;
    margin-top: 30px !important;
}

/* Hearts Animation */
.hearts-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.heart {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff69b4;
    transform: rotate(45deg);
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart:before,
.heart:after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff69b4;
    border-radius: 50%;
}

.heart:before {
    top: -10px;
    left: 0;
}

.heart:after {
    top: 0;
    left: -10px;
}

.heart:nth-child(1) { left: 10%; animation-delay: 0s; }
.heart:nth-child(2) { left: 20%; animation-delay: 1s; }
.heart:nth-child(3) { left: 30%; animation-delay: 2s; }
.heart:nth-child(4) { left: 70%; animation-delay: 3s; }
.heart:nth-child(5) { left: 80%; animation-delay: 4s; }
.heart:nth-child(6) { left: 90%; animation-delay: 5s; }
.heart:nth-child(7) { left: 50%; animation-delay: 2.5s; }
.heart:nth-child(8) { left: 60%; animation-delay: 1.5s; }

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(45deg) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
        transform: translateY(90vh) rotate(45deg) scale(1);
    }
    90% {
        opacity: 0.7;
        transform: translateY(-10vh) rotate(45deg) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .title, .birthday-title {
        font-size: 2rem;
    }
    
    .riddle-text p {
        font-size: 1rem;
    }
    
    .message-box {
        font-size: 1rem;
    }
    
    #answer-input {
        font-size: 1rem;
        padding: 12px 15px;
    }
    
    #submit-btn, #back-btn {
        font-size: 1rem;
        padding: 12px 25px;
    }
}

@media (max-width: 480px) {
    .content {
        padding: 25px 15px;
    }

    .title, .birthday-title {
        font-size: 1.8rem;
    }

    .riddle-box {
        padding: 20px;
    }

    .loading-title {
        font-size: 2rem;
    }

    .cricket-container {
        gap: 15px;
    }

    .cricket {
        font-size: 1.5rem;
    }

    .loading-bar {
        width: 250px;
    }

    .toast {
        padding: 20px 25px;
        margin: 0 20px;
    }

    .balloon {
        font-size: 1.5rem;
    }

    .toast-message {
        font-size: 1rem;
    }
}
