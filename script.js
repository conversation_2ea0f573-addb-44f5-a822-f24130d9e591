// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const toast = document.getElementById('toast');
const riddleSection = document.getElementById('riddle-section');
const messageSection = document.getElementById('message-section');
const answerInput = document.getElementById('answer-input');
const submitBtn = document.getElementById('submit-btn');
const errorMessage = document.getElementById('error-message');

// Correct answer (case-insensitive)
const correctAnswer = 'love';

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Show loading screen first
    showLoadingScreen();

    // Add enter key listener to input field
    answerInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            checkAnswer();
        }
    });

    // Clear error message when user starts typing
    answerInput.addEventListener('input', function() {
        hideErrorMessage();
    });
});

// Function to show loading screen
function showLoadingScreen() {
    loadingScreen.classList.add('active');

    // Hide loading screen after 3 seconds and show riddle
    setTimeout(() => {
        loadingScreen.classList.remove('active');
        setTimeout(() => {
            riddleSection.classList.add('active');
            answerInput.focus();
        }, 800);
    }, 3000);
}

// Function to check the answer
function checkAnswer() {
    const userAnswer = answerInput.value.trim().toLowerCase();
    
    if (userAnswer === '') {
        showErrorMessage('Please enter an answer!');
        return;
    }
    
    if (userAnswer === correctAnswer) {
        // Correct answer - show birthday message
        showBirthdayMessage();
    } else {
        // Wrong answer - show error
        showErrorMessage('Not quite right... Think about what makes life meaningful! 💕');
        answerInput.value = '';
        answerInput.focus();
    }
}

// Function to show birthday message
function showBirthdayMessage() {
    // Add success animation to submit button
    submitBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
    submitBtn.innerHTML = '✓ Correct!';

    // Show toast notification with balloons
    showToast();

    // Wait for toast animation then transition
    setTimeout(() => {
        hideToast();
        riddleSection.classList.remove('active');
        setTimeout(() => {
            messageSection.classList.add('active');
            triggerCelebration();
        }, 400);
    }, 2500);
}

// Function to show toast notification
function showToast() {
    toast.classList.add('show');
}

// Function to hide toast notification
function hideToast() {
    toast.classList.remove('show');
}

// Function to go back to riddle
function goBack() {
    messageSection.classList.remove('active');
    setTimeout(() => {
        riddleSection.classList.add('active');
        // Reset the form
        answerInput.value = '';
        submitBtn.style.background = 'linear-gradient(135deg, #d63384 0%, #6f42c1 100%)';
        submitBtn.innerHTML = 'Unlock Message';
        hideErrorMessage();
        answerInput.focus();
    }, 400);
}

// Function to show error message
function showErrorMessage(message) {
    errorMessage.textContent = message;
    errorMessage.classList.add('show');
    
    // Add shake animation to input
    answerInput.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        answerInput.style.animation = '';
    }, 500);
}

// Function to hide error message
function hideErrorMessage() {
    errorMessage.classList.remove('show');
}

// Add shake animation keyframes dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .pulse {
        animation: pulse 0.6s ease-in-out;
    }
`;
document.head.appendChild(style);

// Add some interactive effects
submitBtn.addEventListener('click', function() {
    this.classList.add('pulse');
    setTimeout(() => {
        this.classList.remove('pulse');
    }, 600);
});

// Add floating hearts effect on correct answer
function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.innerHTML = '💖';
    heart.style.position = 'fixed';
    heart.style.left = Math.random() * 100 + 'vw';
    heart.style.top = '100vh';
    heart.style.fontSize = '2rem';
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = '1000';
    heart.style.animation = 'floatUp 3s ease-out forwards';
    
    document.body.appendChild(heart);
    
    setTimeout(() => {
        heart.remove();
    }, 3000);
}

// Add floating hearts animation
const floatUpStyle = document.createElement('style');
floatUpStyle.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(floatUpStyle);

// Trigger floating hearts when showing birthday message
function triggerCelebration() {
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            createFloatingHeart();
        }, i * 200);
    }
}

// Trigger floating hearts when showing birthday message
function triggerCelebration() {
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            createFloatingHeart();
        }, i * 200);
    }
}
